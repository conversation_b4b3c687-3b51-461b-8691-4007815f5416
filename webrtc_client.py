import asyncio
import logging
import numpy as np
import time
from aiortc import RTCPeerConnection, RTCConfiguration, RTCSessionDescription, RTCIceCandidate
import socketio

import config
from config import (
    RTP_CAPABILITIES, VIDEO_WIDTH, VIDEO_HEIGHT
)

# Global variables for ICE candidate optimization
_working_turn_connection_info = None
_video_pc_candidates = []
_video_pc_gathering_complete = False


def create_rtc_ice_candidate_from_server(server_candidate):
    """
    Convert server-provided ICE candidate to aiortc RTCIceCandidate format.

    Args:
        server_candidate: ICE candidate dict from server response

    Returns:
        RTCIceCandidate: aiortc-compatible ICE candidate
    """
    # Convert server candidate format to aiortc format
    # Server format: {"foundation": "udpcandidate", "ip": "***********", "port": 50795, "priority": 1076302079, "protocol": "udp", "type": "host"}
    # aiortc format: RTCIceCandidate(foundation, component, protocol, priority, ip, port, type)

    try:
        candidate = RTCIceCandidate(
            foundation=server_candidate.get("foundation", "1"),
            component=1,  # Always 1 for RTP
            protocol=server_candidate.get("protocol", "udp").upper(),
            priority=server_candidate.get("priority", 1076302079),
            ip=server_candidate["ip"],
            port=server_candidate["port"],
            type=server_candidate.get("type", "host")
        )
        return candidate
    except Exception as e:
        logging.error(f"Failed to create RTCIceCandidate from server data: {e}")
        logging.error(f"Server candidate: {server_candidate}")
        return None


def setup_ice_gathering_monitoring(peer_connection, pc_name):
    """
    Set up ICE gathering state monitoring for debugging and optimization.
    """
    @peer_connection.on("icegatheringstatechange")
    def on_ice_gathering_state_change():
        state = peer_connection.iceGatheringState
        logging.info(f"🧊 ICE GATHERING [{pc_name}] - State changed to: {state}")

        if state == "complete":
            logging.info(f"🧊 ICE GATHERING [{pc_name}] - Gathering completed")
            if pc_name == "video_pc":
                global _video_pc_gathering_complete
                _video_pc_gathering_complete = True

    @peer_connection.on("iceconnectionstatechange")
    def on_ice_connection_state_change():
        state = peer_connection.iceConnectionState
        logging.info(f"🧊 ICE CONNECTION [{pc_name}] - State changed to: {state}")

        if state == "connected":
            logging.info(f"✅ ICE CONNECTION [{pc_name}] - Successfully established")
        elif state == "completed":
            logging.info(f"✅ ICE CONNECTION [{pc_name}] - Completed successfully")
        elif state == "failed":
            logging.error(f"❌ ICE CONNECTION [{pc_name}] - Failed to establish connection")
        elif state == "disconnected":
            logging.warning(f"⚠️ ICE CONNECTION [{pc_name}] - Connection lost")


def setup_video_pc_candidate_capture(peer_connection):
    """
    Set up candidate capture from video_pc for reuse by other peer connections.
    This captures candidates as they're gathered during normal video PC setup.
    """
    global _video_pc_candidates, _video_pc_gathering_complete

    logging.info("🧊 VIDEO PC GATHER - Setting up candidate capture from video PC")

    # Reset state
    _video_pc_candidates = []
    _video_pc_gathering_complete = False

    @peer_connection.on("icecandidate")
    def on_video_candidate(candidate):
        global _video_pc_candidates
        if candidate:
            _video_pc_candidates.append(candidate)
            logging.debug(f"🧊 VIDEO PC GATHER - Captured candidate: {candidate.candidate}")


async def apply_server_ice_candidates(peer_connection, transport_info, pc_name):
    """
    Apply server-provided ICE candidates directly to a peer connection.
    This should eliminate the need for 5-second ICE gathering.

    Args:
        peer_connection: RTCPeerConnection to apply candidates to
        transport_info: Transport info from server response containing iceCandidates
        pc_name: Name for logging (e.g., "video_pc")
    """
    ice_candidates = transport_info.get("iceCandidates", [])

    if not ice_candidates:
        logging.warning(f"No ICE candidates provided for {pc_name}")
        return

    logging.info(f"🧊 ICE CANDIDATES - Applying {len(ice_candidates)} server-provided candidates to {pc_name}")

    applied_count = 0
    for server_candidate in ice_candidates:
        rtc_candidate = create_rtc_ice_candidate_from_server(server_candidate)
        if rtc_candidate:
            try:
                await peer_connection.addIceCandidate(rtc_candidate)
                applied_count += 1
                logging.debug(f"Applied ICE candidate to {pc_name}: {server_candidate['ip']}:{server_candidate['port']}")
            except Exception as e:
                logging.warning(f"Failed to apply ICE candidate to {pc_name}: {e}")
        else:
            logging.warning(f"Failed to create RTCIceCandidate for {pc_name}")

    logging.info(f"✅ ICE CANDIDATES - Applied {applied_count}/{len(ice_candidates)} candidates to {pc_name}")


async def start_streaming_session():
    # Configure Socket.IO with shorter timeouts for faster connection
    sio = socketio.AsyncClient(logger=True, engineio_logger=True, reconnection=False)
    pc = RTCPeerConnection()
    call_failed_event = asyncio.Event()

    @pc.on("track")
    async def on_track(track):
        if track.kind != "video":
            return
        logging.info("Live video track received. Switching to live feed.")
        config.is_live.set()
        first_frame = True
        try:
            while True:
                frame = await track.recv()
                img = frame.to_image().resize((VIDEO_WIDTH, VIDEO_HEIGHT))
                
                if first_frame:
                    # Use custom save path if specified, otherwise default to last_call.png
                    save_path = config.custom_save_path if config.custom_save_path else "last_call.png"
                    img.save(save_path)
                    logging.info(f"Saved first frame to {save_path}")
                    
                    # Set appropriate completion flag
                    if config.custom_save_path:
                        config.snapshot_saved.set()  # Signal snapshot update complete
                    else:
                        config.frame_saved.set()  # Signal that frame has been saved
                    first_frame = False
                
                img_np = np.array(img, dtype=np.uint8)
                img_bgr = img_np[:, :, ::-1]
                await config.live_frame_queue.put(img_bgr.tobytes())
        except Exception as e:
            logging.info(f"Live video track ended: {e}")
        finally:
            logging.info("Live track finished. Reverting to dummy image.")
            config.is_live.clear()
            await config.live_frame_queue.put(None)

    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        logging.info(f"WebRTC Connection State is {pc.connectionState}")
        if pc.connectionState in ["failed", "closed", "disconnected"]:
            logging.error(f"WebRTC connection {pc.connectionState}.")
            # Immediately clear live feed flag to prevent FFmpeg issues
            config.is_live.clear()
            # Signal end of live frames
            try:
                await config.live_frame_queue.put(None)
            except Exception as e:
                logging.warning(f"Error signaling end of live frames: {e}")
            call_failed_event.set()

    start_time = None
    
    @sio.event
    async def connect():
        nonlocal start_time
        logging.info("Socket.IO connected. Joining call room...")
        start_time = time.time()
        await sio.emit("join_call", {"appToken": config.APP_TOKEN, "roomId": config.ROOM_ID, "fermaxOauthToken": config.FERMAX_OAUTH_TOKEN, "protocolVersion": "0.8.2"}, callback=handle_join_response)

    @sio.event
    async def end_up(data):
        logging.error(f"Call ended by server: {data.get('reason', 'unknown reason')}")
        call_failed_event.set()

    @sio.event
    async def disconnect():
        logging.info("Socket.IO disconnected.")

    async def handle_join_response(response_dict):
        if start_time:
            join_duration = time.time() - start_time
            logging.info(f"Received response for 'join_call' after {join_duration:.2f}s")
        logging.info(f"Full response: {response_dict}")
        try:
            if "result" in response_dict:
                # 🧊 ICE OPTIMIZATION: Setup ICE monitoring and candidate capture
                logging.info("🧊 ICE OPTIMIZATION - Setting up ICE monitoring and candidate capture")
                setup_ice_gathering_monitoring(pc, "video_pc")
                setup_video_pc_candidate_capture(pc)

                await setup_webrtc_handshake_video_only(response_dict["result"])
            else:
                logging.error(f"Server returned error response: {response_dict}")
                call_failed_event.set()
        except Exception as e:
            logging.error(f"Error parsing join response: {e}", exc_info=True)
            call_failed_event.set()

    async def setup_webrtc_handshake_video_only(server_info):
        try:
            logging.info("Starting VIDEO-ONLY WebRTC handshake...")
            pc.configuration = RTCConfiguration(iceServers=server_info["iceServers"])
            video_transport_info = server_info["recvTransportVideo"]
            video_producer_id = server_info["producerIdVideo"]
            video_consume_future = asyncio.Future()
            await sio.emit("transport_consume", {"transportId": video_transport_info["id"], "producerId": video_producer_id, "rtpCapabilities": RTP_CAPABILITIES}, callback=lambda data: video_consume_future.set_result(data))
            video_consumer_params = await video_consume_future
            ice, dtls = video_transport_info["iceParameters"], video_transport_info["dtlsParameters"]
            vid_rtp = video_consumer_params["result"]["rtpParameters"]
            video_payload_type = vid_rtp['codecs'][0]['payloadType']

            # 🧊 ICE OPTIMIZATION: Build SDP with server-provided ICE candidates
            logging.info("🧊 ICE OPTIMIZATION - Building SDP with server-provided ICE candidates")
            sdp_str = (f"v=0\r\no=- 5133548076695286524 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=msid-semantic: WMS\r\n"
                       f"a=ice-ufrag:{ice['usernameFragment']}\r\na=ice-pwd:{ice['password']}\r\n" +
                       "".join([f"a=fingerprint:{fp['algorithm']} {fp['value'].upper()}\r\n" for fp in dtls['fingerprints']]) +
                       f"a=setup:actpass\r\nm=video 9 UDP/TLS/RTP/SAVPF {video_payload_type}\r\nc=IN IP4 0.0.0.0\r\na=rtcp-mux\r\n" +
                       "".join([f"a=candidate:{c['foundation']} 1 {c['protocol']} {c['priority']} {c['ip']} {c['port']} typ {c['type']}\r\n" for c in video_transport_info.get("iceCandidates", [])]) +
                       f"a=end-of-candidates\r\na=mid:video\r\na=sendrecv\r\n" +
                       "".join([f"a=extmap:{ext['id']} {ext['uri']}\r\n" for ext in vid_rtp["headerExtensions"]]) +
                       f"a=rtpmap:{video_payload_type} H264/90000\r\n"
                       f"a=fmtp:{video_payload_type} level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\n"
                       f"a=ssrc:{vid_rtp['encodings'][0]['ssrc']} cname:{vid_rtp['rtcp']['cname']}\r\n")

            remote_description = RTCSessionDescription(sdp=sdp_str, type="offer")
            await pc.setRemoteDescription(remote_description)

            # 🧊 ICE OPTIMIZATION: Apply server-provided ICE candidates after setting remote description
            await apply_server_ice_candidates(pc, video_transport_info, "video_pc")

            answer = await pc.createAnswer()
            await pc.setLocalDescription(answer)
            dtls_transport = pc.getTransceivers()[0].receiver.transport
            local_dtls_params = {"role": "client", "fingerprints": [{"algorithm": fp.algorithm, "value": fp.value} for fp in dtls_transport.getLocalParameters().fingerprints]}
            await sio.emit("transport_connect", {"transportId": video_transport_info["id"], "dtlsParameters": local_dtls_params})
        except Exception as e:
            logging.error(f"Error during WebRTC setup: {e}", exc_info=True)
            call_failed_event.set()

    try:
        # Use websocket transport for faster connection, fallback to polling
        await sio.connect(config.SERVER_URL, transports=["websocket", "polling"], socketio_path="/socket.io")
        
        # Add timeout to WebRTC connection to fail faster
        await asyncio.wait_for(call_failed_event.wait(), timeout=20.0)
    except asyncio.TimeoutError:
        logging.error("WebRTC connection timed out after 20 seconds")
        raise
    except asyncio.CancelledError:
        logging.info("Doorbell session cancelled by user.")
        raise
    finally:
        config.is_live.clear()
        
        # Send end signal to video queue
        try:
            await config.live_frame_queue.put(None)
        except Exception as e:
            logging.warning(f"Failed to send video end signal: {e}")
        
        # Let FFmpeg continue running - stream generator will switch back to dummy content
        logging.info("WebRTC session ended - stream generator will revert to dummy content")
        
        # Send hang_up event before disconnecting (following network capture protocol)
        if sio and sio.connected:
            try:
                logging.info("Sending hang_up event to server")
                await sio.emit("hang_up", {})
                # Wait briefly for server acknowledgment
                await asyncio.sleep(0.1)
            except Exception as e:
                logging.warning(f"Failed to send hang_up event: {e}")
            
            await sio.disconnect()
        if pc and pc.connectionState != 'closed':
            await pc.close()
        logging.info("Doorbell session cleanup complete.")